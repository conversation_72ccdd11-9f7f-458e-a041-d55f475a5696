<!-- 新增/编辑数据来源配置 -->
<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>{{ type === "add" ? "新增" : "编辑" }}数据来源配置</h3>

    <!-- 第一个卡片：数据获取渠道 -->
    <el-card id="dataChannel">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据获取渠道</span>
      </div>
      <DynamicForm
        ref="dataChannelForm"
        :config="dataChannelConfig"
        :params="formParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="220px"
      />
    </el-card>

    <!-- 第二个卡片：数据获取信息 -->
    <el-card id="dataInfo" style="margin-top: 20px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据获取信息</span>
      </div>
      <DynamicForm
        ref="dataInfoForm"
        :config="dataInfoConfig"
        :params="formParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="220px"
      >
        <template #dataMappingRelation>
          <StationConfigTable
            ref="mappingTable"
            :columns="mappingColumns"
            v-model="formParams.dataMappingRelation"
            :showAddBtn="false"
          />
        </template>
      </DynamicForm>
    </el-card>

    <!-- 第三个卡片：数据外传信息 -->
    <el-card id="dataExport" style="margin-top: 20px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据外传信息</span>
      </div>
      <DynamicForm
        ref="dataExportForm"
        :config="dataExportConfig"
        :params="formParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="220px"
      />
    </el-card>

    <!-- 操作按钮 -->
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading">
        取 消
      </el-button>
      <el-button
        @click="submit"
        type="primary"
        size="medium"
        :loading="btnLoading"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import api from "@/api/ledger/configManage/dataOrigin.js";
import ledgerApi from "@/api/ledger/index.js";
import { listDept } from "@/api/common.js";
export default {
  name: "dataOriginAdd",
  components: {
    StationConfigTable,
  },
  data() {
    return {
      type: "add", // add | edit
      pageLoading: false,
      btnLoading: false,
      formParams: {
        // 数据获取渠道
        dataSourceChannel: "钉钉",

        // 数据获取信息
        dingTalkWorkOrderId: "",
        dingTalkWorkOrderName: "",
        pushCondition: "到节点推送",
        nodeName: "",
        tableName: "",
        precondition: "",
        syncFieldNames: "",
        dataMappingRelation: [{}], // 数据映射关系表格数据

        // 数据外传信息
        needPush: 0, // 0:否, 1:是
        pushPrecondition: "",
        pushFieldNames: "",
        middleTableFieldDisposal: "",
      },

      // 下拉选项数据
      supportDeptOptions: [], // 支持部门选项
      businessTypeOptions: [], // 业务类型选项
      orderTypeOptions: [], // 工单类型选项
      urgencyLevelOptions: [], // 紧急程度选项
      demandOriginOptions: [], // 需求来源选项
      userOptions: [], // 用户选项
      orderCreateMethodOptions: [], // 工单创建方式选项
    };
  },
  computed: {
    // 数据获取渠道配置
    dataChannelConfig() {
      return [
        {
          field: "dataSourceChannel",
          title: "数据源渠道：",
          element: "el-select",
          props: {
            options: [
              { label: "钉钉", value: "钉钉" },
              { label: "运管中间表", value: "运管中间表" },
            ],
            optionLabel: "label",
            optionValue: "value",
            placeholder: "请选择数据源渠道",
          },
          rules: [
            {
              required: true,
              message: "请选择数据源渠道",
              trigger: "change",
            },
          ],
          on: {
            change: this.handleChannelChange,
          },
          defaultValue: "钉钉",
        },
      ];
    },

    // 数据获取信息配置
    dataInfoConfig() {
      const isDingTalk = this.formParams.dataSourceChannel === "钉钉";
      const isMiddleTable = this.formParams.dataSourceChannel === "运管中间表";
      const showNodeName =
        isDingTalk && this.formParams.pushCondition === "到节点推送";

      return [
        // 钉钉专用字段
        {
          field: "dingTalkWorkOrderId",
          title: "钉钉工单ID：",
          element: "el-input",
          attrs: {
            placeholder: "请输入钉钉工单类型对应的ID编码",
          },
          rules: [
            {
              required: isDingTalk,
              message: "请输入钉钉工单ID",
              trigger: "blur",
            },
          ],
          show: isDingTalk,
        },
        {
          field: "dingTalkWorkOrderName",
          title: "钉钉工单名称：",
          element: "el-input",
          attrs: {
            placeholder: "请输入钉钉工单类型的名称，如新电途-活动工单",
            maxlength: 100,
            showWordLimit: true,
          },
          rules: [
            {
              required: isDingTalk,
              message: "请输入钉钉工单名称",
              trigger: "blur",
            },
          ],
          show: isDingTalk,
        },
        {
          field: "pushCondition",
          title: "推送的前置条件：",
          element: "el-radio-group",
          props: {
            options: [
              { label: "工单创建成功后", value: "工单创建成功后" },
              { label: "到节点推送", value: "到节点推送" },
            ],
            optionLabel: "label",
            optionValue: "value",
          },
          rules: [
            {
              required: isDingTalk,
              message: "请选择推送的前置条件",
              trigger: "change",
            },
          ],
          on: {
            change: this.handlePushConditionChange,
          },
          show: isDingTalk,
          defaultValue: "到节点推送",
        },
        {
          field: "nodeName",
          title: "节点名称：",
          element: "el-input",
          attrs: {
            placeholder:
              "请输入钉钉工单节点名称，流程到该节点时，将推送数据给维保通",
          },
          rules: [
            {
              required: isDingTalk && showNodeName,
              message: "请输入节点名称",
              trigger: "blur",
            },
          ],
          show: isDingTalk && showNodeName,
        },

        // 运管中间表专用字段
        {
          field: "tableName",
          title: "表名：",
          element: "el-input",
          attrs: {
            placeholder: "请输入表名",
          },
          rules: [
            {
              required: isMiddleTable,
              message: "请输入表名",
              trigger: "blur",
            },
          ],
          show: isMiddleTable,
        },
        {
          field: "precondition",
          title: "前置条件：",
          element: "el-input",
          attrs: {
            placeholder: "自定义取数的SQL规则",
          },
          rules: [
            {
              required: isMiddleTable,
              message: "请输入前置条件",
              trigger: "blur",
            },
          ],
          show: isMiddleTable,
        },

        // 通用字段
        {
          field: "syncFieldNames",
          title: "需同步的字段名称：",
          element: "el-input",
          attrs: {
            placeholder:
              '多个字段名称用，号分割，比如"配置反馈，活动城市，活动类型"',
          },
          rules: [
            {
              required: true,
              message: "请输入需同步的字段名称",
              trigger: "blur",
            },
          ],
        },
        {
          field: "dataMappingRelation",
          title: "同步到维保通工单台账的数据映射关系：",
          element: "slot",
          slotName: "dataMappingRelation",
          rules: [
            {
              required: true,
              message: "请配置数据映射关系",
              trigger: "change",
            },
          ],
        },
      ];
    },

    // 数据外传信息配置
    dataExportConfig() {
      const needPush = this.formParams.needPush === 1;
      const isMiddleTable = this.formParams.dataSourceChannel === "运管中间表";

      return [
        {
          field: "needPush",
          title: "维保通是否需要推送：",
          element: "el-radio-group",
          props: {
            options: [
              { label: "否", value: 0 },
              { label: "是", value: 1 },
            ],
            optionLabel: "label",
            optionValue: "value",
          },
          rules: [
            {
              required: true,
              message: "请选择维保通是否需要推送",
              trigger: "change",
            },
          ],
          on: {
            change: this.handleNeedPushChange,
          },
          defaultValue: 0,
        },
        {
          field: "pushPrecondition",
          title: "外传的前置条件：",
          element: "el-select",
          attrs: {
            placeholder: "请选择外传的前置条件",
          },
          props: {
            options: [], // 根据工单类型动态获取节点名称
            optionLabel: "label",
            optionValue: "value",
            filterable: true,
          },
          rules: [
            {
              required: needPush,
              message: "请选择外传的前置条件",
              trigger: "change",
            },
          ],
          show: needPush,
        },
        {
          field: "pushFieldNames",
          title: "需推送的字段名称：",
          element: "el-input",
          attrs: {
            placeholder:
              '多个字段名称用，号分割，比如"配置反馈，活动城市，活动类型"',
          },
          rules: [
            {
              required: needPush,
              message: "请输入需推送的字段名称",
              trigger: "blur",
            },
          ],
          show: needPush,
        },
        {
          field: "middleTableFieldDisposal",
          title: "中间表侧字段处置：",
          element: "el-input",
          attrs: {
            placeholder: "请输入中间表侧字段处置",
          },
          rules: [
            {
              required: needPush && isMiddleTable,
              message: "请输入中间表侧字段处置",
              trigger: "blur",
            },
          ],
          show: needPush && isMiddleTable,
        },
      ];
    },

    // 数据映射关系表格列配置
    mappingColumns() {
      return [
        {
          field: "supportDept",
          title: "支持部门",
          width: 120,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择支持部门" }],
          props: {
            options: this.supportDeptOptions,
            optionLabel: "supportDeptName",
            optionValue: "supportDept",
            filterable: true,
          },
          on: {
            change: this.handleMappingDeptChange,
          },
        },
        {
          field: "businessType",
          title: "业务类型",
          width: 150,
          isEdit: true,
          element: "el-cascader",
          rules: [{ required: true, message: "请选择业务类型" }],
          props: {
            options: this.businessTypeOptions,
            props: {
              checkStrictly: true,
              multiple: false,
              value: "id",
              label: "typeName",
              children: "childrenList",
              expandTrigger: "hover",
            },
          },
          on: {
            change: this.handleMappingBusinessChange,
          },
        },
        {
          field: "orderType",
          title: "工单类型",
          width: 150,
          isEdit: true,
          element: "el-cascader",
          rules: [{ required: true, message: "请选择工单类型" }],
          props: {
            options: this.orderTypeOptions,
            props: {
              checkStrictly: true,
              multiple: false,
              value: "id",
              label: "typeName",
              children: "childrenList",
              expandTrigger: "hover",
            },
          },
        },
        {
          field: "urgencyLevel",
          title: "紧急程度",
          width: 120,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择紧急程度" }],
          props: {
            options: this.urgencyLevelOptions,
            optionLabel: "urgencyName",
            optionValue: "urgencyId",
            filterable: true,
          },
        },
        {
          field: "problemDesc",
          title: "问题描述",
          width: 200,
          isEdit: true,
          element: "el-input",
          rules: [{ required: true, message: "请输入问题描述" }],
          attrs: {
            placeholder: "{审批单号}为【审批单号】；{活动名称}为【活动名称】",
          },
        },
        {
          field: "demandSource",
          title: "需求来源",
          width: 120,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择需求来源" }],
          props: {
            options: this.demandOriginOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          field: "creator",
          title: "创建人",
          width: 120,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择创建人" }],
          props: {
            options: this.userOptions,
            optionLabel: "nickName",
            optionValue: "userId",
            filterable: true,
          },
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择创建时间" }],
          props: {
            options: [
              { label: "推送时间", value: "pushTime" },
              { label: "上一节点审批通过时间", value: "lastNodeApprovalTime" },
            ],
            optionLabel: "label",
            optionValue: "value",
          },
        },
        {
          field: "orderCreateMethod",
          title: "工单创建方式",
          width: 150,
          isEdit: true,
          element: "el-select",
          rules: [{ required: true, message: "请选择工单创建方式" }],
          props: {
            options: this.orderCreateMethodOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
      ];
    },
  },
  created() {
    this.type = this.$route.query.type || "add";
    if (this.type === "edit") {
      this.loadDetail();
    }
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        this.pageLoading = true;

        // 获取支持部门选项
        ledgerApi.permissionDept({}).then((res) => {
          this.supportDeptOptions = res?.data;
        });

        // 获取字典数据
        await Promise.all([
          this.getDicts("ledger_demand_source").then((res) => {
            this.demandOriginOptions = res.data || [];
          }),
          this.getDicts("order_create_method").then((res) => {
            this.orderCreateMethodOptions = res.data || [];
          }),
        ]);

        // 获取用户列表
        this.getListUser();

        // 初始化映射表格数据
        if (!this.formParams.dataMappingRelation.length) {
          this.formParams.dataMappingRelation = [{}];
        }
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化数据失败");
      } finally {
        this.pageLoading = false;
      }
    },

    // 获取用户列表
    async getListUser() {
      try {
        const res = await ledgerApi.getUserList({
          pageNum: 1,
          pageSize: 9999,
        });
        if (res.code === "10000") {
          this.userOptions = res.data;
        }
      } catch (error) {
        console.error("获取用户列表失败:", error);
      }
    },

    // 加载详情数据
    async loadDetail() {
      try {
        const id = this.$route.query.id;
        if (!id) return;

        this.pageLoading = true;
        const res = await api.getDetail({ id });
        if (res.code === "10000") {
          this.formParams = { ...this.formParams, ...res.data };

          // 确保数据映射关系是数组
          if (!Array.isArray(this.formParams.dataMappingRelation)) {
            this.formParams.dataMappingRelation = [{}];
          } else if (this.formParams.dataMappingRelation.length > 0) {
            // 编辑时根据详情数据获取下拉选项
            const mappingData = this.formParams.dataMappingRelation[0];
            if (mappingData.supportDept) {
              // 根据支持部门获取业务类型
              this.getBusinessOptions(mappingData.supportDept);

              if (
                mappingData.businessType &&
                mappingData.businessType.length > 0
              ) {
                // 根据业务类型获取工单类型和紧急程度
                const businessTypeId =
                  mappingData.businessType[mappingData.businessType.length - 1];
                this.getOrderTypeOptions(
                  mappingData.supportDept,
                  businessTypeId
                );
                this.getUrgencyOptions(
                  mappingData.supportDept,
                  mappingData.businessType[0]
                );
              }
            }
          }
        }
      } catch (error) {
        console.error("加载详情失败:", error);
        this.$message.error("加载详情失败");
      } finally {
        this.pageLoading = false;
      }
    },

    // 数据源渠道变化处理
    handleChannelChange(value) {
      // 清空相关字段
      if (value === "钉钉") {
        this.formParams.tableName = "";
        this.formParams.precondition = "";
      } else if (value === "运管中间表") {
        this.formParams.dingTalkWorkOrderId = "";
        this.formParams.dingTalkWorkOrderName = "";
        this.formParams.pushCondition = "";
        this.formParams.nodeName = "";
      }
    },

    // 推送前置条件变化处理
    handlePushConditionChange(value) {
      if (value === "工单创建成功后") {
        this.formParams.nodeName = "";
      }
    },

    // 维保通是否需要推送变化处理
    handleNeedPushChange(value) {
      if (value === 0) {
        this.formParams.pushPrecondition = "";
        this.formParams.pushFieldNames = "";
        this.formParams.middleTableFieldDisposal = "";
      }
    },

    // 映射表格支持部门变化处理
    handleMappingDeptChange(value) {
      // 清空相关字段
      this.businessTypeOptions = [];
      this.orderTypeOptions = [];
      this.urgencyLevelOptions = [];

      // 根据部门获取业务类型
      this.getBusinessOptions(value);
    },

    // 映射表格业务类型变化处理
    handleMappingBusinessChange(value) {
      // 清空相关字段
      this.orderTypeOptions = [];
      this.urgencyLevelOptions = [];

      const len = value?.length || 0;
      if (len > 0) {
        // 根据业务类型获取工单类型和紧急程度
        this.getOrderTypeOptions(
          this.formParams.dataMappingRelation[0]?.supportDept,
          value[len - 1]
        );
        this.getUrgencyOptions(
          this.formParams.dataMappingRelation[0]?.supportDept,
          value[0]
        );
      }
    },

    // 获取业务类型选项
    getBusinessOptions(supportDept) {
      ledgerApi
        .queryBusinessByDept({ supportDept, permissionFlag: "0" })
        .then((res) => {
          this.businessTypeOptions = res.data || [];
        });
    },

    // 获取工单类型选项
    getOrderTypeOptions(supportDept, businessTypeId) {
      ledgerApi
        .queryOrderOptionsByBusiness({
          supportDept,
          businessTypeId,
          permissionFlag: "0",
        })
        .then((res) => {
          this.orderTypeOptions =
            res.data?.map((x) => {
              return { ...x, disabled: true };
            }) || [];
        });
    },

    // 获取紧急程度选项
    getUrgencyOptions(supportDept, businessType) {
      ledgerApi
        .getUrgencyDegree({
          pageSize: 99999,
          pageNum: 1,
          status: 0,
          supportDept,
          businessType,
        })
        .then((res) => {
          this.urgencyLevelOptions = res.data || [];
        });
    },

    // 表单验证
    async validateForm() {
      try {
        // 验证各个表单
        const dataChannelValid = await this.$refs.dataChannelForm.validate();
        const dataInfoValid = await this.$refs.dataInfoForm.validate();
        const dataExportValid = await this.$refs.dataExportForm.validate();

        // 验证映射表格
        const mappingValid = await this.$refs.mappingTable.validate();

        return (
          dataChannelValid && dataInfoValid && dataExportValid && mappingValid
        );
      } catch (error) {
        console.error("表单验证失败:", error);
        return false;
      }
    },

    // 提交表单
    async submit() {
      try {
        // 表单验证
        const isValid = await this.validateForm();
        if (!isValid) {
          this.$message.warning("请完善表单信息");
          return;
        }

        this.btnLoading = true;

        // 准备提交数据
        const submitData = {
          ...this.formParams,
          id: this.type === "edit" ? this.$route.query.id : undefined,
        };

        // 处理数据映射关系
        if (Array.isArray(submitData.dataMappingRelation)) {
          submitData.dataMappingRelation = JSON.stringify(
            submitData.dataMappingRelation
          );
        }

        const res = await api.update(submitData);
        if (res.code === "10000") {
          this.$message.success("保存成功");
          this.goBack();
        } else {
          this.$message.error(res.message || "保存失败");
        }
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error("提交失败");
      } finally {
        this.btnLoading = false;
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="less" scoped></style>
