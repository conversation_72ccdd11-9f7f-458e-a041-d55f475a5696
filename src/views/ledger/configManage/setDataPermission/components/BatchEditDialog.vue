<template>
  <div class="app-container">
    <el-dialog
      title="批量配置账号数据权限"
      width="70%"
      :visible.sync="visible"
      @close="closeDialog"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog-header">
        <div class="selected-users">
          <span>已选择账号：</span>
          <div class="user-list">
            <span
              v-for="(user, index) in selectedUsers"
              :key="user.userId"
              class="user-item"
            >
              {{ user.nickName + "-" + user.userName }}
              <span v-if="index < selectedUsers.length - 1">；</span>
            </span>
          </div>
        </div>
        <div>
          <el-switch
            v-model="ledgerMaxEnableFlag"
            active-text="最高权限"
            @change="handleStatusChange"
            active-value="0"
            inactive-value="1"
          >
          </el-switch>
          <el-tooltip
            effect="dark"
            content="最高权限：指的是不受数据权限控制，用户在工单台账模块可查看所有的工单类型、业务类型、部门数据"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="工单类型" name="order">
          <el-tabs v-model="deptActive" @tab-click="handleClick">
            <el-tab-pane
              :label="item.dictLabel"
              :name="item.dictValue"
              v-for="item in deptList"
              :key="item.dictValue"
            ></el-tab-pane>
          </el-tabs>
          <TransferTree
            :cascadeData="cascadeData"
            v-model="checkedData"
            ref="transferTree"
            :titles="['未配置', '已配置']"
          ></TransferTree>
        </el-tab-pane>
        <el-tab-pane label="业务类型" name="business">
          <TransferTree
            :cascadeData="businessList"
            v-model="businessChecked"
            ref="businessTree"
            :titles="['未配置', '已配置']"
          ></TransferTree>
        </el-tab-pane>
        <el-tab-pane label="部门" name="dept">
          <div
            style="display: flex;justify-content: flex-end;align-items: center;margin-bottom: 10px;"
          >
            <el-switch
              v-model="ledgerDeptOrderFlag"
              active-text="已配置的部门所有工单类型默认都可见"
              active-value="0"
              inactive-value="1"
            >
            </el-switch>
            <el-tooltip
              effect="dark"
              content="已配置的部门所有工单类型默认都可见：指的是配置了部门后，该部门新增的工单类型以后不需要给账号单独再配置，该部门所有的工单类型均在数据权限范围内"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <el-transfer
            v-model="deptChecked"
            :data="deptList"
            filterable
            :props="{ key: 'dictValue', label: 'dictLabel' }"
            :titles="['未配置', '已配置']"
          ></el-transfer>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryOrderTree,
  queryBusinessTree,
  batchConfigPermission,
} from "@/api/ledger/setPermission.js";

import TransferTree from "@/components/TransferTree/index2.vue";

export default {
  components: { TransferTree },
  data() {
    return {
      deptActive: "",
      visible: false,
      activeName: "order",
      checkedData: [],
      cascadeData: [],
      selectedUsers: [],
      deptChecked: [],
      deptList: [],
      businessChecked: [],
      businessList: [],
      ledgerMaxEnableFlag: "1",
      ledgerDeptOrderFlag: "1",
    };
  },
  watch: {},
  computed: {},
  async created() {
    this.getDicts("support_dept").then((res) => {
      this.deptList = res.data?.map((x) => {
        return { ...x, supportDept: x.dictValue, supportDeptName: x.dictLabel };
      });
      this.deptActive = this.deptList[0]?.dictValue;
      this.getTreeList();
    });
  },
  methods: {
    async handleStatusChange(val) {
      // 批量操作时暂不处理单个状态变更
      console.log("批量最高权限设置:", val);
    },
    async handleClick() {
      if (this.deptActive) {
        await this.getTreeList();
        this.getOrderData();
      }
    },
    //获取部门树及已选项
    async getDeptData() {
      // 批量配置时清空已选项
      this.deptChecked = [];
    },
    //获取业务类型及已选项
    async getBusinessData() {
      const res = await queryBusinessTree({});
      if (res) {
        this.businessList = res.data;
        this.traverseBusiness(this.businessList);
        // 批量配置时清空已选项
        this.businessChecked = [];
        this.$nextTick(() => {
          this.$refs.businessTree?.getDefaultLeftData();
        });
      }
    },
    //获取所有工单类型-树形结构
    async getTreeList() {
      const res = await queryOrderTree({
        supportDepts: [this.deptActive],
      });
      if (res) {
        this.cascadeData = res.data;
        this.traverseBusiness(this.cascadeData);
        console.log(this.cascadeData, "this.cascadeData");
      }
    },
    //业务类型/工单类型转换通用
    traverseBusiness(arr, pid) {
      arr?.forEach((obj) => {
        // 添加id和label
        obj.label = obj.typeName;
        obj.pid = obj.parentId;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        obj["children"] = obj.childrenList || obj.childList || [];
        if (obj.children && obj.children.length > 0) {
          this.traverseBusiness(obj.children, obj.deptId);
        } else {
          delete obj.children;
        }
      });
    },
    async getOrderData() {
      // 批量配置时清空已选项
      this.checkedData = [];
      this.$nextTick(() => {
        this.$refs.transferTree?.getDefaultLeftData();
      });
    },
    async openDialog(selectedUsers) {
      this.visible = true;
      this.selectedUsers = selectedUsers || [];
      this.ledgerMaxEnableFlag = "1";
      this.ledgerDeptOrderFlag = "1";
      this.activeName = "order";
      this.getOrderData();
      this.getBusinessData();
      this.getDeptData();
    },
    closeDialog() {
      this.visible = false;
      this.$emit("submit");
    },
    //提交批量配置
    async handleSubmit() {
      let res;
      const userIds = this.selectedUsers.map((user) => user.userId);

      if (this.activeName === "order") {
        res = await batchConfigPermission({
          type: "order",
          userIds: userIds,
          orderTypeList: this.checkedData?.map((x) => {
            return {
              ...x,
              childrenList: x.children?.map((y) => {
                return { ...y, childrenList: y.children };
              }),
            };
          }),
          supportDept: this.deptActive,
        });
      } else if (this.activeName === "business") {
        res = await batchConfigPermission({
          type: "business",
          userIds: userIds,
          businessTypeList: this.businessChecked,
        });
      } else {
        res = await batchConfigPermission({
          type: "dept",
          userIds: userIds,
          deptList: this.deptList?.filter((x) =>
            this.deptChecked.includes(x.dictValue)
          ),
          ledgerDeptOrderFlag: this.ledgerDeptOrderFlag,
        });
      }

      if (res?.code == 10000) {
        this.$message.success("批量配置成功");
        if (this.activeName === "dept") {
          this.closeDialog();
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;

  .el-transfer-panel {
    flex: 1;
  }

  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}

/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}

.selected-users {
  flex: 1;
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.user-list {
  display: inline-block;
  max-width: 750px;
  overflow-x: auto;
  white-space: nowrap;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #f5f7fa;
}

.user-item {
  display: inline-block;
  margin-right: 5px;
  color: #606266;
}
</style>
