<template>
  <div v-loading="loading">
    <DynamicForm
      ref="baseForm"
      :config="config"
      :params="formParams"
      labelPosition="right"
      :defaultColSpan="24"
      labelWidth="150px"
    >
      <template #stationTable>
        <LuckySheet
          :containerId="'luckysheet-config-' + uniqueId"
          ref="luckySheet"
        />
      </template>
      <template #activeType>
        <div style="display: flex; align-items: center;">
          <el-select
            v-model="formParams.activeType"
            placeholder="请选择活动类型"
            style="width: 200px;"
            filterable
            clearable
            @change="handleActivityTypeChange"
          >
            <el-option
              v-for="item in activeTypeOptions"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            ></el-option>
          </el-select>
          <div style="display: flex; align-items: center; margin-left: 10px;">
            <el-input-number
              v-model="formParams.activeTypeCount"
              :min="0"
              :precision="0"
              controls-position="right"
              style="width: 120px;"
            ></el-input-number>
            <span style="margin-left: 5px;">次</span>
          </div>
        </div>
      </template>
    </DynamicForm>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      modalWidth="50%"
      labelWidth="120px"
    ></BaseFormModal>
    <div class="page-btn">
      <el-button @click.stop="handleClose" size="medium">取 消</el-button>
      <el-button @click.stop="handleReject" size="medium">驳回到配置</el-button>
      <el-button @click="handleSubmit" type="primary" size="medium"
        >确 定</el-button
      >
    </div>
  </div>
</template>

<script>
import api from "@/api/ledger/commonEmail.js";
import { queryCityTree } from "@/api/common.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { initParams } from "@/utils/buse.js";
import LuckySheet from "@/components/LuckySheet/index.vue";

export default {
  name: "commonEmailRecheck",
  components: { BaseFormModal, LuckySheet },
  data() {
    return {
      mailDataId: "",
      differenceTypeOptions: [],
      formParams: {},
      operatorOptions: [],
      regionData: [],
      loading: false,
      activeTypeOptions: [],
      uniqueId: Date.now() + Math.floor(Math.random() * 1000),
      isInitialized: false,
    };
  },
  computed: {
    modalConfig() {
      return {
        modalTitle: "驳回到配置环节",
        formConfig: [
          {
            field: "reviewReason",
            title: "原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [
              {
                required: true,
                trigger: "blur",
                message: "请输入原因",
              },
            ],
          },
        ],
      };
    },
    config() {
      return [
        {
          field: "stationReviewDetails",
          title: "配置结果",
          element: "slot",
          slotName: "stationTable",
          defaultValue: [],
          // rules: [
          //   {
          //     required: true,
          //     trigger: "change",
          //     message: "场站配置明细不能为空",
          //   },
          // ],
        },
        {
          field: "activeType",
          title: "活动类型",
          element: "slot",
          slotName: "activeType",
          rules: [
            { required: true, trigger: "change", message: "活动类型不能为空" },
          ],
          defaultValue: "1",
        },
        {
          field: "activeTypeCount",
          title: "",
          show: false,
          defaultValue: "1",
        },
        {
          field: "stationShareCount",
          title: "场站分润（次）",
          element: "el-input-number",
          props: {
            max: 100,
            min: 0,
            precision: 0,
          },
          rules: [
            { required: true, trigger: "change", message: "场站分润不能为空" },
          ],
          defaultValue: 1,
        },
        {
          field: "reviewInstructions",
          title: "复核说明",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的描述，500个字符以内",
          },
        },
        {
          field: "attachmentFileList",
          title: "上传",
          element: "file-upload",
          props: {
            limit: 99999,
            accept: ".jpg, .jpeg, .png",
            fileMaxSize: 50,
            textTip: "支持批量上传，上传格式为jpg、jpeg、png文件",
          },
        },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    this.getCityRegionData();
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activeTypeOptions = response.data;
    });
    this.initData();
  },
  methods: {
    handleActivityTypeChange(val) {
      if (val == 2 || val == 3) {
        this.formParams.activeTypeCount = 1;
        this.formParams.stationShareCount = 0;
      } else {
        this.formParams.activeTypeCount = 1;
        this.formParams.stationShareCount = 1;
      }
    },
    initData() {
      const { mailId } = this.$route.query;
      if (mailId) {
        this.mailDataId = mailId;
        this.getDetail();
      }
    },
    async getDetail() {
      this.loading = true;
      try {
        const res = await api.getConfigDetail({ mailDataId: this.mailDataId });
        const {
          provinceCode,
          cityCode,
          stationConfigDetails,
          ...rest
        } = res.data;

        this.formParams = {
          ...this.formParams,
          ...rest,
          region:
            provinceCode && cityCode
              ? [provinceCode.slice(0, -4), cityCode.slice(0, -2)]
              : [],
        };

        // 使用新的API方式设置表格数据
        if (stationConfigDetails) {
          try {
            const tableData = JSON.parse(stationConfigDetails);
            this.$nextTick(() => {
              this.$refs.luckySheet.setData(tableData);
            });
          } catch (error) {
            console.error("解析表格数据失败:", error);
            this.$message.warning("表格数据格式错误，将使用空表格");
          }
        }
      } finally {
        this.loading = false;
      }
    },
    modalConfirmHandler(row) {
      api.saveRecheck(row).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("驳回成功");
          this.handleClose();
        }
      });
    },
    handleReject() {
      this.$refs.formModal.open({
        ...this.formParams,
        reviewReason: "",
        mailDataId: this.mailDataId,
        reviewResult: "1",
      });
    },
    async handleSubmit() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return false;

        // 使用新的API方式获取表格数据
        const tableValidation = this.$refs.luckySheet.validateData();
        if (!tableValidation.valid) {
          this.$message.error(`表格数据错误: ${tableValidation.message}`);
          return;
        }

        console.log("获取到的表格数据:", tableValidation.data);

        this.$confirm("是否确认复核完成？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const { region } = { ...this.formParams };

          let params = {
            ...this.formParams,
            provinceCode: region?.[0] ? region?.[0] + "0000" : undefined,
            cityCode: region?.[1] ? region?.[1] + "00" : undefined,
            mailDataId: this.mailDataId,
            reviewResult: "0",
            stationReviewDetails: JSON.stringify(tableValidation.data), // 使用主动获取的数据
          };

          api
            .saveRecheck(params)
            .then(() => {
              this.$message.success("保存成功");
              this.handleClose();
            })
            .catch((error) => {
              console.error("提交失败:", error);
              this.$message.error("提交失败，请检查数据格式");
            });
        });
      });
    },
    handleClose() {
      console.log("点击了取消");
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/ledger/commonEmail");
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
